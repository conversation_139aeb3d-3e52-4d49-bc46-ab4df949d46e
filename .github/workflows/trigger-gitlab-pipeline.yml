name: <PERSON><PERSON> GitLab CI Pipeline

on:
  push:
    branches:
      - master
      - "release/**"
      - "!release/**-published"
      - "pre-release/**"
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  trigger_pipeline:
    runs-on: ubuntu-latest

    steps:
      - name: Print Configs
        run: |
          [[ $GITHUB_EVENT_NAME = "pull_request" ]] && BRANCH_NAME="${{ github.head_ref }}" || BRANCH_NAME="${{ github.ref_name }}"

          [[ $GITHUB_EVENT_NAME = "pull_request" ]] && COMMIT_MSG="${{ github.event.pull_request.title }}" || COMMIT_MSG=$(echo -e "${{ github.event.head_commit.message }}" | head -n 1)

          PIPELINE_PROJECT_URL="github.com/$GITHUB_REPOSITORY.git"

          echo "BRANCH_NAME=$BRANCH_NAME" >> "$GITHUB_ENV"
          echo "COMMIT_MSG=$COMMIT_MSG" >> "$GITHUB_ENV"
          echo "PIPELINE_PROJECT_URL=$PIPELINE_PROJECT_URL" >> "$GITHUB_ENV"

          echo "PIPELINE_PROJECT_URL: $PIPELINE_PROJECT_URL"
          echo "PIPELINE_EVENT: $GITHUB_EVENT_NAME"
          echo "PIPELINE_BRANCH_NAME: $BRANCH_NAME"
          echo "PIPELINE_TRIGGER_PERSON: $GITHUB_ACTOR"
          echo "PIPELINE_COMMIT_MSG: $COMMIT_MSG"

      - name: Trigger GitLab Pipeline
        env:
          GITLAB_TOKEN: ${{ secrets.GITLAB_TOKEN }}
          GITLAB_ENDPOINT: ${{ vars.GITLAB_ENDPOINT }}
          GITLAB_PROJECT_ID: ${{ vars.GITLAB_PROJECT_ID }}
          GITLAB_REPO_NAME: ${{ vars.GITLAB_REPO_NAME }}
        run: |
          response=$(curl -sS --request POST \
              --fail \
              --form token=$GITLAB_TOKEN \
              --form "ref=main" \
              --form "variables[PIPELINE_PROJECT_NAME]=$GITLAB_REPO_NAME" \
              --form "variables[PIPELINE_PROJECT_URL]=$PIPELINE_PROJECT_URL" \
              --form "variables[PIPELINE_EVENT]=$GITHUB_EVENT_NAME" \
              --form "variables[PIPELINE_BRANCH_NAME]=$BRANCH_NAME" \
              --form "variables[PIPELINE_TRIGGER_PERSON]=$GITHUB_ACTOR" \
              --form "variables[PIPELINE_COMMIT_MSG]=$COMMIT_MSG" \
              --url "$GITLAB_ENDPOINT/$GITLAB_PROJECT_ID/trigger/pipeline")

          echo "Pipeline URL: $(echo $response | jq -r '.web_url')"

          PIPELINE_ID=$(echo $response | jq -r '.id')
          echo "PIPELINE_ID=$PIPELINE_ID" >> "$GITHUB_ENV"

          echo "Pipeline ID: $PIPELINE_ID"

      - name: Check GitLab Pipeline
        env:
          GITLAB_PAT: ${{ secrets.GITLAB_PAT }}
          GITLAB_ENDPOINT: ${{ vars.GITLAB_ENDPOINT }}
          GITLAB_PROJECT_ID: ${{ vars.GITLAB_PROJECT_ID }}
          SLEEP_DURATION_MINUTES: 20
          CHECK_INTERVAL_MINUTES: 5
          MAX_RETRY_ATTEMPTS: 5
          DOWNSTREAM_PIPELINE_NAME: library
          DOWNSTREAM_JOB_NAME: build-dist-job
        run: |
          echo "Pipeline ID: $PIPELINE_ID"
          echo "[ Running pipeline check after $SLEEP_DURATION_MINUTES minutes ]"
          sleep $(($SLEEP_DURATION_MINUTES*60))

          num_attempts=1
          job_status=""
          until [[ $num_attempts -gt $MAX_RETRY_ATTEMPTS || $job_status == "success" || $job_status == "failed" || $job_status == "canceled" || $job_status == "skipped" ]]
          do
            echo "Attempt: $num_attempts"

            downstream_pipeline_id=$(curl -sS --request GET --header "PRIVATE-TOKEN: $GITLAB_PAT" \
              --url "$GITLAB_ENDPOINT/$GITLAB_PROJECT_ID/pipelines/$PIPELINE_ID/bridges" \
              | jq -r '[ .[] | select( .name | contains("'"$DOWNSTREAM_PIPELINE_NAME"'")) ]' | jq -r '.[].downstream_pipeline.id')
            echo "($DOWNSTREAM_PIPELINE_NAME) Downstream pipeline id: $downstream_pipeline_id"

            job_status=$(curl -sS --header "PRIVATE-TOKEN: $GITLAB_PAT" \
              --url "$GITLAB_ENDPOINT/$GITLAB_PROJECT_ID/pipelines/$downstream_pipeline_id/jobs" \
              | jq -r '[ .[] | select( .name | contains("'"$DOWNSTREAM_JOB_NAME"'")) ]' | jq -r '.[].status')
            echo "($DOWNSTREAM_JOB_NAME) Job status: $job_status"

            if [[ $job_status == "running" ]]; then
              num_attempts=$((num_attempts+1))
            fi

            if [[ $num_attempts -lt $MAX_RETRY_ATTEMPTS ]]; then
              sleep $(($CHECK_INTERVAL_MINUTES*60))
            fi
          done

          [[ $num_attempts -gt $MAX_RETRY_ATTEMPTS ]] && echo "Number of retries exceeded, please head to the GitLab job to check what happened" && exit 1

          [[ $job_status == "canceled" ]] && echo "GitLab job was cancelled"

          [[ $job_status == "skipped" ]] && echo "GitLab job has stopped, please head to the GitLab job to trigger job manually"

          [[ $job_status == "failed" ]] && echo "GitLab job has failed, please head to the GitLab job to check on error" && exit 1

          [[ $job_status == "success" ]] && echo "GitLab job is successfully completed, please head to the GitLab job for further deployments"
