<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v2/css/main.css"
/>

<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v3/css/main.css"
/>

<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v3/css/open-sans.css"
/>

<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v3/css/plus-jakarta-sans.css"
/>

<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v3/css/lato.css"
/>

<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v3/css/atkinson-hyperlegible-next.css"
/>

<link
    rel="stylesheet"
    href="https://assets.life.gov.sg/react-design-system/v3/css/libre-franklin.css"
/>

<style>
    /* custom storybook styles here */

    /* class applied by `storybook-dark-mode` addon */
    .storybook-dark-mode {
        background-color: black;
    }

    .storybook-dark-mode .docs-story {
        background-color: black;
    }

    .storybook-light-mode {
        background-color: white;
    }

    .storybook-light-mode .docs-story {
        background-color: white;
    }
</style>
