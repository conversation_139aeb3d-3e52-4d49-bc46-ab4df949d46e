export const componentMap = [
    {
        oldName: "DesignToken",
        newName: "V2_DesignToken",
    },
    {
        oldName: "DesignTokenSet",
        newName: "V2_DesignTokenSet",
    },
    {
        oldName: "DesignTokenSetOptions",
        newName: "V2_DesignTokenSetOptions",
    },
    {
        oldName: "MediaQuery",
        newName: "V2_MediaQuery",
    },
    {
        oldName: "MediaWidths",
        newName: "V2_MediaWidths",
    },
    {
        oldName: "MediaWidth",
        newName: "V2_MediaWidth",
    },
    {
        oldName: "MediaType",
        newName: "V2_MediaType",
    },
    {
        oldName: "Color",
        newName: "V2_Color",
    },
    {
        oldName: "ColorSet",
        newName: "V2_ColorSet",
    },
    {
        oldName: "ValidationElementAttributes",
        newName: "V2_ValidationElementAttributes",
    },
    {
        oldName: "ValidationTypes",
        newName: "V2_ValidationTypes",
    },
    {
        oldName: "ColorSetOptions",
        newName: "V2_ColorSetOptions",
    },
    {
        oldName: "Text",
        newName: "V2_Text",
    },
    {
        oldName: "TextStyleHelper",
        newName: "V2_TextStyleHelper",
    },
    {
        oldName: "TextStyle",
        newName: "V2_TextStyle",
    },
    {
        oldName: "TextSizeType",
        newName: "V2_TextSizeType",
    },
    {
        oldName: "TextLinkSizeType",
        newName: "V2_TextLinkSizeType",
    },
    {
        oldName: "TextStyleSpec",
        newName: "V2_TextStyleSpec",
    },
    {
        oldName: "TextStyleSetType",
        newName: "V2_TextStyleSetType",
    },
    {
        oldName: "TextStyleSetOptionsType",
        newName: "V2_TextStyleSetOptionsType",
    },
    {
        oldName: "TextWeight",
        newName: "V2_TextWeight",
    },
    {
        oldName: "TextProps",
        newName: "V2_TextProps",
    },
    {
        oldName: "TextLinkProps",
        newName: "V2_TextLinkProps",
    },
    {
        oldName: "TextLinkStyleProps",
        newName: "V2_TextLinkStyleProps",
    },
    {
        oldName: "Layout",
        newName: "V2_Layout",
    },
    {
        oldName: "ColDiv",
        newName: "V2_ColDiv",
    },
    {
        oldName: "Container",
        newName: "V2_Container",
    },
    {
        oldName: "Content",
        newName: "V2_Content",
    },
    {
        oldName: "Section",
        newName: "V2_Section",
    },
    {
        oldName: "CommonLayoutProps",
        newName: "V2_CommonLayoutProps",
    },
    {
        oldName: "SectionProps",
        newName: "V2_SectionProps",
    },
    {
        oldName: "ContainerType",
        newName: "V2_ContainerType",
    },
    {
        oldName: "ContainerProps",
        newName: "V2_ContainerProps",
    },
    {
        oldName: "ContentProps",
        newName: "V2_ContentProps",
    },
    {
        oldName: "DivRef",
        newName: "V2_DivRef",
    },
    {
        oldName: "ColProps",
        newName: "V2_ColProps",
    },
    {
        oldName: "ColDivProps",
        newName: "V2_ColDivProps",
    },
    {
        oldName: "TextList",
        newName: "V2_TextList",
    },
    {
        oldName: "OrderedListProps",
        newName: "V2_OrderedListProps",
    },
    {
        oldName: "UnorderedListProps",
        newName: "V2_UnorderedListProps",
    },
    {
        oldName: "CounterType",
        newName: "V2_CounterType",
    },
    {
        oldName: "BulletType",
        newName: "V2_BulletType",
    },
    {
        oldName: "Transition",
        newName: "V2_Transition",
    },
    // Added theme name mappings
    {
        oldName: "BaseTheme",
        newName: "V2_BaseTheme",
    },
    {
        oldName: "BookingSGTheme",
        newName: "V2_BookingSGTheme",
    },
    {
        oldName: "RBSTheme",
        newName: "V2_RBSTheme",
    },
    {
        oldName: "MyLegacyTheme",
        newName: "V2_MyLegacyTheme",
    },
    {
        oldName: "CCubeTheme",
        newName: "V2_CCubeTheme",
    },
    {
        oldName: "OneServiceTheme",
        newName: "V2_OneServiceTheme",
    },
    // Added type name mappings
    {
        oldName: "ThemeSpec",
        newName: "V2_ThemeSpec",
    },
    {
        oldName: "ThemeSpecOptions",
        newName: "V2_ThemeSpecOptions",
    },
    {
        oldName: "ThemeCollectionSpec",
        newName: "V2_ThemeCollectionSpec",
    },
    {
        oldName: "ColorScheme",
        newName: "V2_ColorScheme",
    },
    {
        oldName: "TextStyleScheme",
        newName: "V2_TextStyleScheme",
    },
    {
        oldName: "DesignTokenScheme",
        newName: "V2_DesignTokenScheme",
    },
    {
        oldName: "ResourceScheme",
        newName: "V2_ResourceScheme",
    },
    {
        oldName: "ColorCollectionsMap",
        newName: "V2_ColorCollectionsMap",
    },
    {
        oldName: "FontStyleCollectionsMap",
        newName: "V2_FontStyleCollectionsMap",
    },
    {
        oldName: "DesignTokenCollectionsMap",
        newName: "V2_DesignTokenCollectionsMap",
    },
    {
        oldName: "ThemeContextKeys",
        newName: "V2_ThemeContextKeys",
    },
    {
        oldName: "ThemeLayout",
        newName: "V2_ThemeLayout",
    },
];

export const pathMap = [
    {
        oldPath: "design-token",
        newPath: "v2_design-token",
    },
    {
        oldPath: "color",
        newPath: "v2_color",
    },
    {
        oldPath: "media",
        newPath: "v2_media",
    },
    {
        oldPath: "text",
        newPath: "v2_text",
    },
    {
        oldPath: "layout",
        newPath: "v2_layout",
    },
    {
        oldPath: "text-list",
        newPath: "v2_text-list",
    },
    {
        oldPath: "theme",
        newPath: "v2_theme",
    },
    {
        oldPath: "transition",
        newPath: "v2_transition",
    },
];
