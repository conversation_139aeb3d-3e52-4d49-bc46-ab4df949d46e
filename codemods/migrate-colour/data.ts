const defaultMapping = {
    Primary: "primary-50",
    PrimaryDark: "primary-40",
    Secondary: "primary-40",
    "Brand[1]": "brand-50",
    "Brand[2]": "brand-60",
    "Brand[3]": "brand-70",
    "Brand[4]": "brand-80",
    "Brand[5]": "brand-90",
    "Brand[6]": "brand-95",
    "Accent.Light[1]": "primary-60",
    "Accent.Light[2]": "primary-70",
    "Accent.Light[3]": "primary-80",
    "Accent.Light[4]": "primary-90",
    "Accent.Light[5]": "primary-95",
    "Accent.Light[6]": "primary-100",
    "Accent.Dark[1]": "secondary-40",
    "Accent.Dark[2]": "secondary-50",
    "Accent.Dark[3]": "secondary-60",
    "Neutral[1]": "neutral-20",
    "Neutral[2]": "neutral-30",
    "Neutral[3]": "neutral-50",
    "Neutral[4]": "neutral-60",
    "Neutral[5]": "neutral-90",
    "Neutral[6]": "neutral-95",
    "Neutral[7]": "neutral-100",
    "Neutral[8]": "white",
    "Validation.Green.Text": "success-40",
    "Validation.Green.Icon": "success-70",
    "Validation.Green.Border": "success-80",
    "Validation.Green.Background": "success-100",
    "Validation.Orange.Text": "warning-50",
    "Validation.Orange.Icon": "warning-70",
    "Validation.Orange.Border": "warning-80",
    "Validation.Orange.Background": "warning-100",
    "Validation.Orange.Badge": "warning-100",
    "Validation.Red.Text": "error-50",
    "Validation.Red.Icon": "error-50",
    "Validation.Red.Border": "error-60",
    "Validation.Red.Background": "error-100",
    "Validation.Blue.Text": "info-40",
    "Validation.Blue.Icon": "info-50",
    "Validation.Blue.Border": "info-70",
    "Validation.Blue.Background": "info-95",
};

export const lifesgMapping = {
    ...defaultMapping,
    Primary: "primary-50",
    PrimaryDark: "primary-40",
    Secondary: "primary-40",
};

export const bookingSgMapping = {
    ...defaultMapping,
    Primary: "primary-50",
    PrimaryDark: "primary-40",
    Secondary: "primary-40",
    "Accent.Dark[1]": "secondary-40",
    "Accent.Dark[2]": "secondary-60",
    "Accent.Dark[3]": "secondary-70",
};

export const mylegacyMapping = {
    ...defaultMapping,
    PrimaryDark: "primary-40",
    Primary: "primary-50",
    Secondary: "primary-40",
    "Validation.Green.Text": "success-50",
    "Validation.Red.Text": "success-40",
};

export const ccubeMapping = {
    ...defaultMapping,
    Primary: "primary-50",
    PrimaryDark: "primary-40",
    Secondary: "secondary-40",
    "Brand[1]": "brand-60",
    "Brand[2]": "brand-50",
};

export const rbsMapping = {
    ...defaultMapping,
};

export const oneServiceMapping = {
    ...defaultMapping,
    Primary: "primary-50",
    PrimaryDark: "primary-40",
    Secondary: "secondary-40",
    "Brand[1]": "brand-60",
    "Accent.Dark[1]": "secondary-50",
    "Accent.Dark[2]": "secondary-60",
    "Accent.Dark[3]": "secondary-70",
};
