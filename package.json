{"name": "@lifesg/react-design-system", "version": "3.0.0-alpha.14", "description": "A component design system for LifeSG web apps", "main": "dist/cjs/index.js", "module": "dist/index.js", "typings": "dist/index.d.ts", "bin": {"lifesg-react-design-system": "./codemods/run-codemod.js"}, "scripts": {"build": "npm run rollup && npm run post:build", "build-check": "npm run rollup rollup.check.config.js", "rollup": "rm -rf dist && rollup --bundleConfigAsCjs -c", "pack-package": "cd dist && npm pack", "post:build": "node ./scripts/post-build.js", "publish-lib": "npm publish ./dist", "start": "rollup -c -w", "test": "jest --coverage", "test-watch": "jest --watch", "test-e2e": "playwright test", "prepare": "(test -d ./.git && npx husky install) || true", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "codemod": "ENV=dev npx tsx codemods/run-codemod.ts"}, "repository": {"type": "git", "url": "git+https://github.com/LifeSG/react-design-system.git"}, "keywords": ["design-system"], "author": "LifeSG", "license": "ISC", "bugs": {"url": "https://github.com/LifeSG/react-design-system/issues"}, "homepage": "https://github.com/LifeSG/react-design-system#readme", "dependencies": {"fabric": "^6.7.0", "immer": "^10.0.2", "react-slider": "^2.0.6", "react-virtuoso": "^4.12.5", "react-zoom-pan-pinch": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.16.11", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@floating-ui/dom": "^1.6.10", "@floating-ui/react": "^0.26.23", "@inquirer/prompts": "^7.0.0", "@lifesg/react-icons": "^1.6.0", "@playwright/test": "^1.45.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/addon-storysource": "^8.6.12", "@storybook/addon-themes": "^8.6.12", "@storybook/addon-webpack5-compiler-swc": "^1.0.3", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-webpack5": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/types": "^8.6.12", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^14.5.2", "@types/dompurify": "^2.4.0", "@types/jest": "^29.5.12", "@types/jscodeshift": "^0.11.11", "@types/lodash": "^4.14.180", "@types/node": "^22.15.14", "@types/react": "^17.0.43", "@types/react-dom": "^17.0.14", "@types/react-slider": "^1.3.6", "@types/styled-components": "^5.1.3", "@typescript-eslint/eslint-plugin": "^5.16.0", "@typescript-eslint/parser": "^5.16.0", "babel-loader": "^8.2.4", "babel-plugin-styled-components": "^2.0.6", "css-loader": "^5.2.7", "dayjs": "^1.11.3", "dompurify": "^3.2.5", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-mdx": "^2.0.4", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.8.0", "fs-extra": "^11.3.0", "html-react-parser": "^2.0.0", "husky": "^7.0.4", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-canvas-mock": "^2.3.1", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-styled-components": "^7.2.0", "jscodeshift": "^17.3.0", "lint-staged": "^12.3.7", "lodash": "^4.17.21", "lottie-colorify": "^0.8.0", "lottie-react": "^2.3.1", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "prettier": "^2.6.1", "pretty": "^2.0.0", "pretty-quick": "^3.1.3", "react": "^17.0.2", "react-docgen-typescript": "^2.2.2", "react-dom": "^17.0.2", "react-dropzone": "^14.2.3", "react-intersection-observer": "^9.5.2", "react-resize-detector": "^7.0.0", "react-responsive": "^9.0.0-beta.10", "react-spring": "~9.7.3", "remark-gfm": "^4.0.0", "rollup": "^3.29.4", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-generate-package-json": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-typescript2": "^0.36.0", "storybook": "^8.6.12", "storybook-dark-mode": "^4.0.2", "style-loader": "^2.0.0", "styled-components": "^5.3.5", "typescript": "^5.7.3"}, "peerDependencies": {"@floating-ui/react": ">=0.26.23 <1.0.0", "@lifesg/react-icons": "^1.5.0", "react": "^17.0.2 || ^18.0.0", "react-dom": "^17.0.2 || ^18.0.0", "styled-components": "^5.3.5"}, "lint-staged": {"*.{tsx,ts}": ["eslint --fix"]}}