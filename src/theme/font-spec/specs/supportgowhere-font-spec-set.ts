import { FontSpecSet } from "../types";

export const SupportGoWhereFontSpecSet: FontSpecSet = {
    "heading-size-xxl": "3rem",
    "heading-size-xl": "2.5rem",
    "heading-size-lg": "2rem",
    "heading-size-md": "1.625rem",
    "heading-size-sm": "1.375rem",
    "heading-size-xs": "1.125rem",

    "heading-lh-xxl": "3.5rem",
    "heading-lh-xl": "3rem",
    "heading-lh-lg": "2.5rem",
    "heading-lh-md": "2.25rem",
    "heading-lh-sm": "1.75rem",
    "heading-lh-xs": "1.625rem",

    "heading-ls-xxl": "-0.056rem",
    "heading-ls-xl": "-0.032rem",
    "heading-ls-lg": "-0.032rem",
    "heading-ls-md": "0rem",
    "heading-ls-sm": "0rem",
    "heading-ls-xs": "0rem",

    "weight-light": "300",
    "weight-regular": "400",
    "weight-semibold": "600",
    "weight-bold": "700",
    "font-family": "Libre Franklin",

    "body-size-baseline": "1.125rem",
    "body-size-md": "1rem",
    "body-size-sm": "0.875rem",
    "body-size-xs": "0.75rem",

    "body-lh-baseline": "1.625rem",
    "body-lh-md": "1.5rem",
    "body-lh-sm": "1.625rem",
    "body-lh-xs": "1.25rem",

    "body-ls-baseline": "0rem",
    "body-ls-md": "0.014rem",
    "body-ls-sm": "0.012rem",
    "body-ls-xs": "0.012rem",

    "form-label-size": "1rem",
    "form-description-size": "0.875rem",

    "form-label-lh": "1.5rem",
    "form-description-lh": "1.6rem",

    "form-label-ls": "0.014rem",
    "form-description-ls": "0.012rem",
};
