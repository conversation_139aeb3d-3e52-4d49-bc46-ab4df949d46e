import { Meta } from "@storybook/blocks";
import { DocInfo } from "stories/storybook-common";

<Meta title="Getting started/Installation" />

# Installation

<DocInfo type="warning">
    {
        <p>
            You are referring to v3 of the design system. You can find the v2
            documentation{" "}
            <a
                href="https://designsystem.life.gov.sg/react/v2/index.html"
                target="_blank"
            >
                here
            </a>
            .
        </p>
    }
</DocInfo>

### 1. Install the Flagship Design System package

```
npm i @lifesg/react-design-system
```

The following peer dependencies should be installed:

```json
{
    "@floating-ui/dom": "^1.6.10",
    "@floating-ui/react": "^0.26.23",
    "@lifesg/react-icons": "^1.10.0",
    "react": "^17.0.2 || ^18.0.0",
    "react-dom": "^17.0.2 || ^18.0.0",
    "styled-components": "^5.3.5"
}
```

### 2. Add the CSS stylesheet

The module requires some custom css for fonts and certain components to work
properly. You will need to load the corresponding stylesheets in your app.

<details open name="css">

<summary>Instructions for LifeSG theme</summary>

Add this into the `head` of your html file:

```tsx
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/main.css"
/>
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/open-sans.css"
/>
```

Or if you are importing to an existing css file:

```css
@import url("https://assets.life.gov.sg/react-design-system/v3/css/main.css");
@import url("https://assets.life.gov.sg/react-design-system/v3/css/open-sans.css");
```

</details>

<br />

<details name="css">

<summary>Instructions for BookingSG theme</summary>

Add this into the `head` of your html file:

```html
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/main.css"
/>
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/plus-jakarta-sans.css"
/>
```

Or if you are importing to an existing css file:

```css
@import url("https://assets.life.gov.sg/react-design-system/v3/css/main.css");
@import url("https://assets.life.gov.sg/react-design-system/v3/css/plus-jakarta-sans.css");
```

</details>

<br />

<details name="css">

<summary>Instructions for PA theme</summary>

Add this into the `head` of your html file:

```html
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/main.css"
/>
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/lato.css"
/>
```

Or if you are importing to an existing css file:

```css
@import url("https://assets.life.gov.sg/react-design-system/v3/css/main.css");
@import url("https://assets.life.gov.sg/react-design-system/v3/css/lato.css");
```

</details>

<br />

<details name="css">

<summary>Instructions for A11y Playground theme</summary>

Add this into the `head` of your html file:

```html
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/main.css"
/>
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/atkinson-hyperlegible-next.css"
/>
```

Or if you are importing to an existing css file:

```css
@import url("https://assets.life.gov.sg/react-design-system/v3/css/main.css");
@import url("https://assets.life.gov.sg/react-design-system/v3/css/atkinson-hyperlegible-next.css");
```

</details>

<br />

<details name="css">

<summary>Instructions for SupportGoWhere theme</summary>

Add this into the `head` of your html file:

```html
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/main.css"
/>
<link
    rel="stylesheet"
    type="text/css"
    href="https://assets.life.gov.sg/react-design-system/v3/css/libre-franklin.css"
/>
```

Or if you are importing to an existing css file:

```css
@import url("https://assets.life.gov.sg/react-design-system/v3/css/main.css");
@import url("https://assets.life.gov.sg/react-design-system/v3/css/libre-franklin.css");
```

</details>

### 3. Set up the theme

If you are intending to make use of the themes, you will need [**Styled
Components**](https://styled-components.com/docs).

```tsx
// app.tsx
import { ThemeProvider } from "styled-components";
import { LifeSGTheme } from "@lifesg/react-design-system/theme";
import { Component } from "./index";

const App = () => {
    return (
        <ThemeProvider theme={LifeSGTheme}>
            <Component />
        </ThemeProvider>
    );
};

export default App;
```

For more information on the usage of themes, you can visit our
[documentation](/docs/foundations-themes-introduction--docs) about it.

### 4. Augment theme types (optional, recommended)

If you access the theme object via the `useTheme` hook in a Typescript project,
you can type it by adding a custom declaration file. Read more at the [Styled
Components api
docs](https://styled-components.com/docs/api#create-a-declarations-file).

```ts
// styled.d.ts
import "styled-components";

import { ThemeSpec } from "@lifesg/react-design-system/theme/types";

declare module "styled-components" {
    export interface DefaultTheme extends ThemeSpec {
        maxColumns?: {
            xxs: 8;
            xs: 8;
            sm: 8;
            md: 8;
            lg: 12;
            xl: 12;
            xxl: 12;
        };
    }
}
```
