import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@storybook/blocks";
import * as SingpassButtonStories from "./singpass-button.stories";
import { PropsTable } from "./props-table";

<Meta of={SingpassButtonStories} />

# SingpassButton

## Overview

The call to action for Singpass login, referencing the
[guidelines](https://docs.developer.singpass.gov.sg/docs/products/login/singpass-button-guidelines-for-developers-and-designers).

```tsx
import { SingpassButton } from "@lifesg/react-design-system/singpass-button";
```

The `SingpassButton` comes in 3 sizes: default, small, and large.

<Canvas of={SingpassButtonStories.Default} />

## Component tokens

This component can be customised via the `Button` tokens.

<Story of={SingpassButtonStories.TokenCustomisation} />

## Component API

<PropsTable />
