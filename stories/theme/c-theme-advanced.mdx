import { Meta } from "@storybook/blocks";
import { DocInfo } from "stories/storybook-common";

<Meta title="Foundations/Themes/Advanced usage" />

# Advanced usage

<DocInfo>
    To use themes, you will need [**Styled
    Components**](https://styled-components.com/docs)
</DocInfo>

This section provides additional information on using themes in your project.

## Theme customisation

### Defining a theme

You can mix and match the schemes to construct a custom theme specification.

```tsx
// app.tsx
import { ThemeSpec } from "@lifesg/react-design-system/theme";
import { ThemeProvider } from "styled-components";
import { Component } from "./index";

const myTheme: ThemeSpec = {
    colourScheme: "bookingsg",
    fontScheme: "default",
    motionScheme: "default",
    borderScheme: "default",
    spacingScheme: "default",
    radiusScheme: "default",
    breakpointScheme: "default",
    resourceScheme: "mylegacy",
};

const App = () => {
    return (
        <ThemeProvider theme={myTheme}>
            <Component />
        </ThemeProvider>
    );
};

export default App;
```

### Extending theme presets

There are also cases where the design style you are going for is a minor variant
of the existing presets. You can selectively modify design tokens by using the
`overrides` prop.

For more information on defining the override value, refer to the respective
documentation for the design token.

{/* TODO: update the properties */}

```tsx
// app.tsx
import { LifeSGTheme, ThemeSpec } from "@lifesg/react-design-system/theme";
import { ThemeProvider } from "styled-components";
import { Component } from "./index";

const customTheme: ThemeSpec = {
    ...LifeSGTheme,
    overrides: {
        primitiveColour: {
            "primary-10": "#F3C85C",
        },
        font: {
            "heading-size-xxl": "4rem",
            "heading-lh-xxl": "4.5rem",
            "heading-ls-xxl": "0.056rem",
        },
    },
};

const App = () => {
    return (
        <ThemeProvider theme={myCustomTheme}>
            <Component />
        </ThemeProvider>
    );
};

export default App;
```

## Styling individual elements

You can change the theme on an element-specific level. Note that this does not
change all the other components in your UI.

In this example, here is how we usually specify properties that are dependent on
the theme e.g. `Colour`

```tsx
import { Colour } from "@lifesg/react-design-system/theme";
import styled, { css } from "styled-components";

const SomeComponent = styled.div`
    background: ${Colour["background-error"]};
`;

// If you extract the props, remember to wrap nested interpolations with the css helper
const AnotherComponent = styled.div`
    ${(props) => {
        return css`
            background: ${Colour["background-error"](props)};
        `;
    }}
`;
```

But with the modified props approach, here's an extra step you will need to do.

```tsx
const SomeComponent = styled.div`
    ${(props) => {
        const modifiedProps = {
            ...props,
            theme: {
                ...props.theme,
                colourScheme: "bookingsg",
            },
        };

        return `
            background: ${Colour["background-error"](modifiedProps)};
        `;
    }}
`;
```
