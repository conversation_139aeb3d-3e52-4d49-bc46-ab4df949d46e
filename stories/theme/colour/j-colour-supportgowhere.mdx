import { Meta } from "@storybook/blocks";
import { SupportGoWhereTheme } from "src/theme";
import { PrimitiveColourDisplay, SemanticColourDisplay } from "../doc-elements";

<Meta title="Foundations/Colours/SupportGoWhere" />

# SupportGoWhere colours

These are the palettes used when the `colourScheme` is `"supportgowhere"`.

```tsx
const theme: ThemeSpec = {
    colourScheme: "supportgowhere",
    // ...other specifications
};
```

## Semantic colours

<SemanticColourDisplay theme={SupportGoWhereTheme} />

## Primitive colours

<PrimitiveColourDisplay theme={SupportGoWhereTheme} />
