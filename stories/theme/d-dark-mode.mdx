import { Meta } from "@storybook/blocks";
import { DocInfo } from "stories/storybook-common";

<Meta title="Foundations/Themes/Dark mode" />

# Dark mode

<DocInfo>
    To use dark mode themes, you will need [**Styled
    Components**](https://styled-components.com/docs)
</DocInfo>

The design system provides built-in support for dark mode that automatically
adapt to system preferences or can be explicitly controlled.

## Basic implementation

Use `DSThemeProvider` for automatic light/dark mode switching based on the
user's system preference. The appropriate colour tokens will be applied.

```tsx
// app.tsx
import {
    DSThemeProvider,
    LifeSGTheme,
} from "@lifesg/react-design-system/theme";
import { Component } from "./index";

const App = () => {
    return (
        <DSThemeProvider theme={LifeSGTheme}>
            <Component />
        </DSThemeProvider>
    );
};

export default App;
```

### When to use explicit variants

You can use the explicit `.light` or `.dark` variants when you need to
**override** the automatic behavior.

#### Force light mode only

```tsx
// Always light mode, ignores system preference
<DSThemeProvider theme={LifeSGTheme.light}>
    <Component />
</DSThemeProvider>
```

#### Force dark mode only

```tsx
// Always dark mode, ignores system preference
<DSThemeProvider theme={LifeSGTheme.dark}>
    <Component />
</DSThemeProvider>
```

#### Manual toggle (user preference)

```tsx
// When you have a toggle switch in your UI
const [userPreference, setUserPreference] = useState<"light" | "dark" | "auto">(
    "auto"
);
const currentTheme =
    userPreference === "dark"
        ? LifeSGTheme.dark
        : userPreference === "light"
        ? LifeSGTheme.light
        : LifeSGTheme;

<DSThemeProvider theme={currentTheme}>
    <Component />
</DSThemeProvider>;
```

## Theme customisation

### Extending dark mode themes

You can extend existing themes and customise colours for both light and dark
modes:

```tsx
// app.tsx
import { LifeSGTheme, ThemeSpec } from "@lifesg/react-design-system/theme";

const customTheme: ThemeSpec = {
    ...LifeSGTheme,
    overrides: {
        // Light mode overrides
        semanticColour: {
            text: "#2c2c2c",
            "bg-primary": "#0066cc",
        },
        // Dark mode specific overrides
        semanticColourDark: {
            text: "#f0f0f0",
            "bg-primary": "#4d94ff",
        },
    },
};
```

## Advanced usage

### Accessing current theme mode

Use the `useDSTheme` hook to access the current colour mode in your components:

```tsx
import { useDSTheme } from "@lifesg/react-design-system/theme";

const MyComponent = () => {
    const { colourMode, theme } = useDSTheme();

    return (
        <div>
            Current mode: {colourMode}
            {colourMode === "dark" && <span>🌙</span>}
            {colourMode === "light" && <span>☀️</span>}
        </div>
    );
};
```

## Migration from existing themes

### Step 1: Update your ThemeProvider

```tsx
// Before - Basic theme usage (still works!)
import { ThemeProvider } from "styled-components";
import { LifeSGTheme } from "@lifesg/react-design-system/theme";

<ThemeProvider theme={LifeSGTheme}>
    <App />
</ThemeProvider>;

// After - Recommended approach with automatic dark mode
import {
    DSThemeProvider,
    LifeSGTheme,
} from "@lifesg/react-design-system/theme";

<DSThemeProvider theme={LifeSGTheme}>
    <App />
</DSThemeProvider>;
```

### Step 2: Add dark mode overrides (if you have custom colours)

```tsx
// Before - Basic customization (still works!)
const customTheme = {
    ...LifeSGTheme,
    overrides: {
        semanticColour: { text: "#custom" },
        primitiveColour: { "primary-50": "#custom" },
    },
};

// After - With explicit dark mode customization
const customTheme = {
    ...LifeSGTheme,
    overrides: {
        // Primitive colours
        primitiveColour: { "primary-50": "#0066cc" },
        primitiveColourDark: { "primary-50": "#4d94ff" },
        // Semantic colours
        semanticColour: { text: "#custom-light" },
        semanticColourDark: { text: "#custom-dark" },
    },
};
```

### Step 3: Test both modes

Test by changing your system preference or using browser dev tools to simulate
`prefers-color-scheme`. Your existing styled components will automatically
adapt.
