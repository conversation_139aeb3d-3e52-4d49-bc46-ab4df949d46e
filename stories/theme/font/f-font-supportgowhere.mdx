import { Meta } from "@storybook/blocks";
import { SupportGoWhereTheme } from "src/theme";
import { DocInfo } from "stories/storybook-common";
import { FontDisplay } from "../doc-elements";

<Meta title="Foundations/Font/SupportGoWhere" />

# SupportGoWhere font

This is the font set used when the `fontScheme` is `"supportgowhere"`.

```tsx
const theme: ThemeSpec = {
    fontScheme: "supportgowhere",
    // ...other specifications
};
```

This stylesheet should be loaded in your app.

```
https://assets.life.gov.sg/react-design-system/v3/css/libre-franklin.css
```

<DocInfo>
    [Common
    ligatures](https://developer.mozilla.org/en-US/docs/Web/CSS/font-variant-ligatures)
    are **disabled** for this font
</DocInfo>

<FontDisplay theme={SupportGoWhereTheme} />
