
import { Color } from "@lifesg/react-design-system/color";
import { MediaQuery } from "@lifesg/react-design-system";
import { MediaWidths as DSMediaWidth } from "@lifesg/react-design-system"
import { Text, TextStyleHelper } from "@lifesg/react-design-system/text"
import { Layout } from "@lifesg/react-design-system/layout";
import { DesignToken } from "@lifesg/react-design-system/design-token";
import { ColDiv } from "@lifesg/react-design-system/layout";
import {ContainerType} from "@lifesg/react-design-system/layout"
import {BaseColorSet} from "@lifesg/react-design-system/layout"
import {TextList} from "@lifesg/react-design-system/text-list"

const Container = styled.div`
   color: ${DesignToken.Table.Cell.Primary};
   color: ${Color.Validation.Red.Text};
`

const Header = styled(Text.H1)``;

interface Props {
    foo : ContainerType;
}

const Component = () => (
    <>
        <DesignToken />
        <Color />
        <MediaQuery />
        <Text.body />
        <Layout.Content type="flex" stretch={true}>
            <div>Item 1</div>
            <div>Item 2</div>
            <div>Item 3</div>
        </Layout.Content>
        <TextList.Ul bulletType="circle">
            <li>First</li>
            <li>Second</li>
            <li>Third</li>
        </TextList.Ul>
    </>
);

export default Component;
