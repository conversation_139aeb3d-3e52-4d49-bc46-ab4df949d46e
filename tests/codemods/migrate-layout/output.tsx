
import { V2_Layout } from "@lifesg/react-design-system/v2_layout";

<div>
    <V2_Layout.ColDiv desktopCols={12} tabletCols={8} mobileCols={4}>
        <TextComponent>{children}</TextComponent>
        <V2_Layout.Container>
            {content}
        </V2_Layout.Container>
        <V2_Layout.Section>
            <V2_Layout.Container type="grid" style={{ padding: "2rem 0", rowGap: "1rem" }}>
                {children}
            </V2_Layout.Container>
        </V2_Layout.Section>
    </V2_Layout.ColDiv>

    <V2_Layout.Container>
            {content}
    </V2_Layout.Container>

</div>

const Container = styled(V2_Layout.Container)``;
